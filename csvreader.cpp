#include "csvreader.h"
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QDebug>
#include <QStandardPaths>
#include <QCoreApplication>

CsvReader::CsvReader(QObject *parent)
    : QObject(parent)
{
}















// ==================== 新增：专门用于图表的高效数据读取方法 ====================









QString CsvReader::getCurrentCsvFilePath(const QString &boilerName)
{
    QDate today = QDate::currentDate();
    return buildCsvFilePath(boilerName, today);
}

// 高效的图表数据解析方法 - 只提取O2和CO数据
QVariantMap CsvReader::parseChartDataLine(const QString &line, qint64 baseTimestamp)
{
    QVariantMap result;

    QStringList fields = line.split(',');
    if (fields.size() < 3) {  // 至少需要时间戳、O2、CO
        return result;
    }

    bool ok;
    qint64 timestamp = fields[0].toLongLong(&ok);
    if (!ok) {
        return result;
    }

    double o2 = fields[1].toDouble();
    double co = fields[2].toDouble();

    // 计算相对时间（如果提供了基准时间戳）
    double relativeTimeHours = 0.0;
    double relativeTimeMinutes = 0.0;

    if (baseTimestamp > 0) {
        qint64 elapsedSeconds = timestamp - baseTimestamp;
        relativeTimeHours = elapsedSeconds / 3600.0;
        relativeTimeMinutes = elapsedSeconds / 60.0;
    }

    result["timestamp"] = timestamp;
    result["x"] = relativeTimeHours;
    result["x_minutes"] = relativeTimeMinutes;
    result["o2"] = o2;
    result["co"] = co;

    return result;
}

// 优化的批量数据解析 - 专门为图表设计
QList<CsvReader::ChartDataPoint> CsvReader::parseChartDataOptimized(const QString &filePath, int maxPoints)
{
    QList<ChartDataPoint> result;

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Cannot open CSV file for chart data:" << filePath;
        return result;
    }

    QTextStream in(&file);

    // 跳过表头
    if (!in.atEnd()) {
        in.readLine();
    }

    QStringList lines;

    // 如果指定了最大点数，从文件末尾开始读取
    if (maxPoints > 0) {
        // 读取所有行到内存
        while (!in.atEnd()) {
            QString line = in.readLine().trimmed();
            if (!line.isEmpty()) {
                lines.append(line);
            }
        }

        // 如果行数超过最大点数，只取最后的部分
        if (lines.size() > maxPoints) {
            lines = lines.mid(lines.size() - maxPoints);
        }
    } else {
        // 读取所有数据
        while (!in.atEnd()) {
            QString line = in.readLine().trimmed();
            if (!line.isEmpty()) {
                lines.append(line);
            }
        }
    }

    file.close();

    // 解析数据并计算相对时间
    qint64 baseTimestamp = 0;

    for (int i = 0; i < lines.size(); ++i) {
        const QString &line = lines[i];
        QStringList fields = line.split(',');

        if (fields.size() < 3) {
            continue;
        }

        bool ok;
        qint64 timestamp = fields[0].toLongLong(&ok);
        if (!ok) {
            continue;
        }

        // 设置基准时间戳（第一个有效数据点的时间）
        if (baseTimestamp == 0) {
            baseTimestamp = timestamp;
        }

        ChartDataPoint point;
        point.timestamp = timestamp;
        point.o2 = fields[1].toDouble();
        point.co = fields[2].toDouble();

        // 计算相对时间
        qint64 elapsedSeconds = timestamp - baseTimestamp;
        point.relativeTimeHours = elapsedSeconds / 3600.0;
        point.relativeTimeMinutes = elapsedSeconds / 60.0;

        result.append(point);
    }

    qDebug() << "Parsed chart data:" << result.size() << "points from" << filePath;
    return result;
}

// 将内部数据结构转换为QML兼容格式
QVariantList CsvReader::convertToQmlFormat(const QList<ChartDataPoint> &dataPoints)
{
    QVariantList result;
    result.reserve(dataPoints.size());

    for (const ChartDataPoint &point : dataPoints) {
        QVariantMap dataPoint;
        dataPoint["timestamp"] = point.timestamp;
        dataPoint["x"] = point.relativeTimeHours;
        dataPoint["x_minutes"] = point.relativeTimeMinutes;
        dataPoint["o2"] = point.o2;
        dataPoint["co"] = point.co;
        result.append(dataPoint);
    }

    return result;
}
