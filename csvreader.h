#ifndef CSVREADER_H
#define CSVREADER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QVariantList>
#include <QVariantMap>
#include <QDateTime>

class CsvReader : public QObject
{
    Q_OBJECT

public:
    explicit CsvReader(QObject *parent = nullptr);

    // 原有的完整数据读取方法（保持兼容性）
    Q_INVOKABLE QVariantList readDataByDate(const QString &boilerName, const QDate &date);
    Q_INVOKABLE QVariantList readDataByDateRange(const QString &boilerName, const QDate &startDate, const QDate &endDate);
    Q_INVOKABLE QStringList getAvailableDates(const QString &boilerName);
    Q_INVOKABLE bool hasDataForDate(const QString &boilerName, const QDate &date);



private:
    // 原有的私有方法
    QString buildCsvFilePath(const QString &boilerName, const QDate &date);
    QVariantMap parseCsvLine(const QString &line);
    bool isValidCsvFile(const QString &filePath);


};

#endif // CSVREADER_H
