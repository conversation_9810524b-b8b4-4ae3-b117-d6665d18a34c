#ifndef CSVREADER_H
#define CSVREADER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QVariantList>
#include <QVariantMap>
#include <QDateTime>

class CsvReader : public QObject
{
    Q_OBJECT

public:
    explicit CsvReader(QObject *parent = nullptr);





private:
    // 原有的私有方法
    QString buildCsvFilePath(const QString &boilerName, const QDate &date);
    QVariantMap parseCsvLine(const QString &line);
    bool isValidCsvFile(const QString &filePath);


};

#endif // CSVREADER_H
